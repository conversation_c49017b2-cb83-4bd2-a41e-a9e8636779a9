import sys
import time
import random
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QLabel, QPushButton, QLineEdit,
    QVBoxLayout, QHBoxLayout, QFrame, QStyleFactory
)
from PyQt6.QtCore import QTimer, Qt, Q<PERSON><PERSON>tyAnimation, QPoint, QEvent
from PyQt6.QtGui import QFont, QKeyEvent

# --- 設定 ---
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600

class ClickSpeedTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("クリック速度測定ツール")
        self.setFixedSize(WINDOW_WIDTH, WINDOW_HEIGHT)

        # --- フォント設定 ---
        self._setup_fonts()

        # --- 変数初期化 ---
        self._setup_variables()

        # --- UIのセットアップ ---
        self._setup_ui()

        # --- 初期状態の設定 ---
        self.show_idle_screen()

    def _setup_fonts(self):
        self.title_font = QFont("Meiryo", 60, QFont.Weight.Bold)
        self.main_font = QFont("Meiryo", 48)
        self.small_font = QFont("Meiryo", 24)
        self.button_font = QFont("Meiryo", 16, QFont.Weight.Bold)
        self.entry_font = QFont("Meiryo", 16)

    def _setup_variables(self):
        self.game_state = "idle"
        self.clicks = 0
        self.click_times = []
        self.start_time = 0
        self.measurement_duration = 10

    def _setup_ui(self):
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # --- 各画面のウィジェットを作成 ---
        self._create_idle_widgets()
        self._create_countdown_widgets()
        self._create_measurement_widgets()
        self._create_finished_widgets()

    def _create_idle_widgets(self):
        self.idle_frame = QFrame(self)
        layout = QVBoxLayout(self.idle_frame)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        settings_layout = QHBoxLayout()
        settings_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        duration_label = QLabel("測定時間(秒):")
        duration_label.setFont(self.entry_font)
        self.duration_entry = QLineEdit(str(self.measurement_duration))
        self.duration_entry.setFont(self.entry_font)
        self.duration_entry.setFixedWidth(80)
        self.duration_entry.setAlignment(Qt.AlignmentFlag.AlignCenter)
        settings_layout.addWidget(duration_label)
        settings_layout.addWidget(self.duration_entry)
        layout.addLayout(settings_layout)

        self.start_button = QPushButton("スタート")
        self.start_button.setFont(self.button_font)
        self.start_button.clicked.connect(self.prepare_game)
        self.start_button.setAutoDefault(False)
        layout.addWidget(self.start_button, alignment=Qt.AlignmentFlag.AlignCenter)
        self.main_layout.addWidget(self.idle_frame)

    def _create_countdown_widgets(self):
        self.countdown_label = QLabel(self)
        self.countdown_label.setFont(self.title_font)
        self.countdown_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.main_layout.addWidget(self.countdown_label)

    def _create_measurement_widgets(self):
        self.measurement_frame = QFrame(self)
        layout = QVBoxLayout(self.measurement_frame)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.time_label = QLabel()
        self.time_label.setFont(self.main_font)
        self.clicks_label = QLabel()
        self.clicks_label.setFont(self.main_font)
        self.cps_label = QLabel()
        self.cps_label.setFont(self.main_font)
        layout.addWidget(self.time_label)
        layout.addWidget(self.clicks_label)
        layout.addWidget(self.cps_label)
        self.main_layout.addWidget(self.measurement_frame)

    def _create_finished_widgets(self):
        self.finished_frame = QFrame(self)
        layout = QVBoxLayout(self.finished_frame)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.finish_title_label = QLabel("終了!")
        self.finish_title_label.setFont(self.title_font)
        self.results_label1 = QLabel()
        self.results_label1.setFont(self.main_font)
        self.results_label2 = QLabel()
        self.results_label2.setFont(self.main_font)
        self.retry_button = QPushButton("リトライ")
        self.retry_button.setFont(self.button_font)
        self.retry_button.clicked.connect(self.show_idle_screen)
        self.retry_button.setAutoDefault(False)

        layout.addWidget(self.finish_title_label)
        layout.addWidget(self.results_label1)
        layout.addWidget(self.results_label2)
        layout.addWidget(self.retry_button)
        self.main_layout.addWidget(self.finished_frame)

    # --- 画面表示の切り替え ---
    def show_idle_screen(self):
        self.game_state = "idle"
        self.idle_frame.show()
        self.countdown_label.hide()
        self.measurement_frame.hide()
        self.finished_frame.hide()
        self.start_button.setEnabled(True) # スタートボタンを有効化
        self.retry_button.setEnabled(False) # リトライボタンを無効化（念のため）

    def prepare_game(self):
        try:
            duration = int(self.duration_entry.text())
            self.measurement_duration = duration if duration > 0 else 10
        except ValueError:
            self.measurement_duration = 10
        self.idle_frame.hide()
        self.start_button.setEnabled(False) # スタートボタンを無効化
        self.shake_window()

    def shake_window(self):
        self.animation = QPropertyAnimation(self, b"pos")
        self.animation.setDuration(800) # 揺れの時間を少し長くして、より多くのキーフレームを表現
        pos = self.pos()
        
        # 揺れのオフセットを大きくし、キーフレームを細かく設定
        offset = 80 # 揺れの最大オフセット
        num_shakes = 100 # 揺れの回数を増やす
        
        for i in range(num_shakes):
            x_offset = random.randint(-offset, offset)
            y_offset = random.randint(-offset, offset)
            self.animation.setKeyValueAt(i / num_shakes, pos + QPoint(x_offset, y_offset))
        
        self.animation.setKeyValueAt(1.0, pos) # 最後に元の位置に戻す

        # 色を白黒フェードに変えるタイマー
        self.fade_value = 0 # 黒からスタート
        self.fade_direction = 1 # 白へ向かう
        self.fade_timer = QTimer(self)
        self.fade_timer.timeout.connect(self._update_fade_color)
        self.fade_timer.start(30) # 30msごとに色を更新

        self.animation.finished.connect(self.start_countdown)
        self.animation.finished.connect(self.fade_timer.stop) # アニメーション終了時にタイマーを停止
        self.animation.finished.connect(lambda: self.setStyleSheet("")) # アニメーション終了時にスタイルシートをリセット
        self.animation.start()

    def _update_fade_color(self):
        self.fade_value += self.fade_direction * 50
        if self.fade_value >= 255:
            self.fade_value = 255
            self.fade_direction = -1 # 白に到達したら黒へ反転
        elif self.fade_value <= 0:
            self.fade_value = 0
            self.fade_direction = 1 # 黒に到達したら白へ反転

        self.setStyleSheet(f"background-color: rgb({self.fade_value}, {self.fade_value}, {self.fade_value});")

    def start_countdown(self):
        self.game_state = "countdown"
        # 他画面を非表示にする
        self.idle_frame.hide()
        self.measurement_frame.hide()
        self.finished_frame.hide()

        self.countdown_label.show()
        self.countdown_value = 3
        self.countdown_timer = QTimer(self)
        self.countdown_timer.timeout.connect(self.update_countdown)
        self.countdown_timer.start(1000)
        self.update_countdown()

    def update_countdown(self):
        if self.countdown_value > 0:
            self.countdown_label.setText(str(self.countdown_value))
            self.countdown_value -= 1
        else:
            self.countdown_timer.stop()
            self.countdown_label.setText("スタート!")
            QTimer.singleShot(1000, self.start_measurement)

    def start_measurement(self):
        self.countdown_label.hide()
        self.measurement_frame.show()
        self.game_state = "measuring"
        self.clicks = 0
        self.click_times = []
        self.start_time = time.time()
        self.measurement_timer = QTimer(self)
        self.measurement_timer.timeout.connect(self.update_measurement_display)
        self.measurement_timer.start(50)

    def update_measurement_display(self):
        elapsed_time = time.time() - self.start_time
        remaining_time = self.measurement_duration - elapsed_time
        if remaining_time > 0:
            self.time_label.setText(f"残り時間: {remaining_time:.1f}")
            self.clicks_label.setText(f"クリック数: {self.clicks}")
            current_time = time.time()
            recent_clicks = [t for t in self.click_times if current_time - t <= 1]
            self.cps_label.setText(f"CPS: {len(recent_clicks)}")
        else:
            self.measurement_timer.stop()
            self.finish_game()

    def finish_game(self):
        self.measurement_frame.hide()
        self.finished_frame.show()
        self.game_state = "finished"
        final_cps = self.clicks / self.measurement_duration if self.measurement_duration > 0 else 0
        self.results_label1.setText(f"総クリック数: {self.clicks}")
        self.results_label2.setText(f"平均CPS: {final_cps:.2f}")
        self.retry_button.setEnabled(True) # リトライボタンを有効化

    def mousePressEvent(self, event):
        if self.game_state == "measuring" and event.button() == Qt.MouseButton.LeftButton:
            self.clicks += 1
            self.click_times.append(time.time())

if __name__ == "__main__":
    app = QApplication(sys.argv)
    # スタイルを設定したい場合は、以下のコメントを解除します
    # app.setStyle(QStyleFactory.keys()[0]) # 例: 利用可能な最初のスタイルを適用
    window = ClickSpeedTest()
    window.show()
    sys.exit(app.exec())